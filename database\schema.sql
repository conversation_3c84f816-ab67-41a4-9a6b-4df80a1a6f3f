-- 在线报名系统数据库表结构
-- <AUTHOR>
-- @license http://www.178188.xyz
-- @lastmodify 2025年8月4日

-- 创建数据库
CREATE DATABASE IF NOT EXISTS registration_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE registration_system;

-- 活动表
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT '活动标题',
    description TEXT COMMENT '活动描述',
    target_audience VARCHAR(255) COMMENT '目标对象',
    address VARCHAR(500) COMMENT '活动地址',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    registration_start DATETIME COMMENT '报名开始时间',
    registration_end DATETIME COMMENT '报名结束时间',
    quota INT DEFAULT 0 COMMENT '报名上限',
    status TINYINT DEFAULT 0 COMMENT '状态：0草稿，1发布，2结束',
    qr_code_url VARCHAR(500) COMMENT '二维码URL',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动表';

-- 报名记录表
CREATE TABLE registrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    event_id BIGINT NOT NULL,
    phone VARCHAR(20) NOT NULL COMMENT '报名手机号',
    sms_status TINYINT DEFAULT 0 COMMENT '短信状态：0待发送，1成功，2失败',
    sms_sent_at DATETIME COMMENT '短信发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES activities(id),
    UNIQUE KEY uk_event_phone (event_id, phone),
    INDEX idx_event_id (event_id),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报名记录表';

-- 验证码表
CREATE TABLE sms_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    code VARCHAR(10) NOT NULL,
    event_id BIGINT,
    expired_at DATETIME NOT NULL,
    used TINYINT DEFAULT 0 COMMENT '是否已使用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_phone_event (phone, event_id),
    INDEX idx_expired (expired_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码表';

-- 管理员表
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) COMMENT '管理员姓名',
    status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1启用',
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 短信发送记录表
CREATE TABLE sms_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    type TINYINT NOT NULL COMMENT '类型：1验证码，2报名通知',
    content TEXT COMMENT '短信内容',
    status TINYINT DEFAULT 0 COMMENT '状态：0发送中，1成功，2失败',
    yunpian_response TEXT COMMENT '云片网响应',
    event_id BIGINT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_event_id (event_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信发送记录表';
