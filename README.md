# 在线报名系统

## 项目介绍

本系统是一套便捷的 H5 在线活动报名平台，支持移动端用户通过二维码进入报名页面，实现活动发布、报名登记、短信通知、统计导出等核心功能。

## 技术栈

- **后端**: Node.js + Express + MySQL + Redis
- **前端H5**: Vue 3 + Vite + TailwindCSS  
- **管理后台**: Vue 3 + Element Plus + Vite
- **短信服务**: 云片网API
- **部署**: Nginx + PM2

## 项目结构

```
registration-system/
├── backend/                 # 后端API服务
│   ├── src/                # 源代码
│   ├── config/             # 配置文件
│   └── logs/               # 日志文件
├── frontend/               # 前端项目
│   ├── h5/                # H5用户端
│   └── admin/             # 管理后台
├── database/               # 数据库脚本
│   ├── schema.sql         # 表结构
│   └── init-data.sql      # 初始数据
├── scripts/                # 部署脚本
├── nginx/                  # Nginx配置
└── ui-prototypes/          # UI原型设计
```

## 快速开始

### 1. 环境要求

- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Git

### 2. 安装依赖

```bash
# 安装所有项目依赖
npm run install:all
```

### 3. 数据库配置

```bash
# 连接MySQL并执行数据库脚本
mysql -h ************ -u root -p < database/schema.sql
mysql -h ************ -u root -p < database/init-data.sql
```

### 4. 环境配置

复制并修改后端环境配置：
```bash
cp backend/.env.example backend/.env
```

### 5. 启动开发服务

```bash
# 启动所有服务（后端API + H5前端 + 管理后台）
npm run dev
```

访问地址：
- H5用户端: http://localhost:5173
- 管理后台: http://localhost:5174  
- 后端API: http://localhost:3000

## 功能特性

### H5用户端
- ✅ 活动列表浏览和筛选
- ✅ 活动详情查看
- ✅ 手机验证码报名
- ✅ 报名状态查询
- ✅ 微信浏览器兼容

### 管理后台
- ✅ 管理员登录认证
- ✅ 活动创建和管理
- ✅ 二维码生成下载
- ✅ 报名数据统计
- ✅ Excel数据导出
- ✅ 短信批量补发

### 后端API
- ✅ RESTful API设计
- ✅ JWT身份认证
- ✅ API限流防护
- ✅ 云片网短信集成
- ✅ Redis缓存优化

## 部署说明

### 生产环境部署

1. **构建前端项目**
```bash
npm run build
```

2. **配置Nginx**
```bash
cp nginx/registration.conf /etc/nginx/sites-available/
ln -s /etc/nginx/sites-available/registration.conf /etc/nginx/sites-enabled/
```

3. **启动后端服务**
```bash
cd backend
npm start
```

4. **使用PM2管理进程**
```bash
pm2 start ecosystem.config.js
```

## 默认账号

- **管理后台**: admin / 123456

## 开发团队

- **作者**: 苏昱
- **许可证**: http://www.178188.xyz

## 更新日志

### v1.0.0 (2025-08-04)
- ✅ 完成项目基础架构搭建
- ✅ 实现数据库设计和初始化
- �� 后端API服务开发中...
