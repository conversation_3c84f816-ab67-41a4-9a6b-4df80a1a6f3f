/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: JWT认证中间件
 */

const jwt = require('jsonwebtoken');
const { redisClient } = require('../config/database');
const Admin = require('../models/Admin');

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'registration_system_secret_key_2025';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

/**
 * 生成JWT token
 * @param {Object} payload - 载荷数据
 * @returns {String} JWT token
 */
function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'registration-system',
    audience: 'admin'
  });
}

/**
 * 验证JWT token
 * @param {String} token - JWT token
 * @returns {Object} 解码后的载荷
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'registration-system',
      audience: 'admin'
    });
  } catch (error) {
    throw new Error('Token验证失败');
  }
}

/**
 * 将token加入黑名单（Redis）
 * @param {String} token - JWT token
 * @param {Number} expireTime - 过期时间（秒）
 */
async function blacklistToken(token, expireTime = 86400) {
  try {
    await redisClient.setEx(`blacklist:${token}`, expireTime, 'true');
  } catch (error) {
    console.error('Token黑名单添加失败:', error);
  }
}

/**
 * 检查token是否在黑名单中
 * @param {String} token - JWT token
 * @returns {Boolean} 是否在黑名单中
 */
async function isTokenBlacklisted(token) {
  try {
    const result = await redisClient.get(`blacklist:${token}`);
    return result === 'true';
  } catch (error) {
    console.error('Token黑名单检查失败:', error);
    return false;
  }
}

/**
 * 认证中间件
 * 验证请求头中的JWT token
 */
const authenticateToken = async (req, res, next) => {
  try {
    // 获取Authorization头
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        code: 3001,
        message: '缺少访问令牌'
      });
    }

    // 检查token是否在黑名单中
    const isBlacklisted = await isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        code: 3002,
        message: '令牌已失效'
      });
    }

    // 验证token
    const decoded = verifyToken(token);
    
    // 验证管理员是否存在且状态正常
    const adminModel = new Admin();
    const admin = await adminModel.findOne({ 
      id: decoded.adminId, 
      status: 1 
    });

    if (!admin) {
      return res.status(401).json({
        success: false,
        code: 3003,
        message: '管理员账户不存在或已被禁用'
      });
    }

    // 将管理员信息添加到请求对象
    req.admin = {
      id: admin.id,
      username: admin.username,
      name: admin.name
    };
    req.token = token;

    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        code: 3004,
        message: '令牌已过期'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        code: 3005,
        message: '无效的令牌'
      });
    }

    return res.status(401).json({
      success: false,
      code: 3000,
      message: '认证失败'
    });
  }
};

/**
 * 可选认证中间件
 * 如果有token则验证，没有token则跳过
 */
const optionalAuth = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  try {
    const decoded = verifyToken(token);
    const adminModel = new Admin();
    const admin = await adminModel.findOne({ 
      id: decoded.adminId, 
      status: 1 
    });

    if (admin) {
      req.admin = {
        id: admin.id,
        username: admin.username,
        name: admin.name
      };
      req.token = token;
    }
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    console.warn('可选认证失败:', error.message);
  }

  next();
};

/**
 * 权限检查中间件
 * @param {Array} requiredRoles - 需要的角色列表
 */
const requireRoles = (requiredRoles = []) => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        code: 3001,
        message: '需要登录'
      });
    }

    // 目前系统只有管理员角色，后续可扩展
    if (requiredRoles.length > 0 && !requiredRoles.includes('admin')) {
      return res.status(403).json({
        success: false,
        code: 3006,
        message: '权限不足'
      });
    }

    next();
  };
};

/**
 * 刷新token
 * @param {String} oldToken - 旧token
 * @param {Object} payload - 新的载荷数据
 * @returns {String} 新token
 */
async function refreshToken(oldToken, payload) {
  try {
    // 将旧token加入黑名单
    await blacklistToken(oldToken);
    
    // 生成新token
    return generateToken(payload);
  } catch (error) {
    throw new Error('Token刷新失败');
  }
}

module.exports = {
  generateToken,
  verifyToken,
  blacklistToken,
  isTokenBlacklisted,
  authenticateToken,
  optionalAuth,
  requireRoles,
  refreshToken
};
