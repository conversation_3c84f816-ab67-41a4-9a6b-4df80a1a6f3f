<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动列表 - 在线报名系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 微信浏览器兼容性 */
        body { -webkit-text-size-adjust: 100%; }
        .activity-card { transition: all 0.3s ease; }
        .activity-card:active { transform: scale(0.98); }
        
        /* 状态标签样式 */
        .status-draft { @apply bg-gray-100 text-gray-600; }
        .status-active { @apply bg-green-100 text-green-600; }
        .status-full { @apply bg-red-100 text-red-600; }
        .status-ended { @apply bg-gray-100 text-gray-500; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
        <div class="px-4 py-3">
            <h1 class="text-lg font-semibold text-gray-900">活动报名</h1>
        </div>
    </header>

    <!-- 筛选栏 -->
    <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex space-x-2 overflow-x-auto">
            <button class="filter-btn active px-4 py-2 rounded-full text-sm font-medium bg-blue-500 text-white whitespace-nowrap">
                全部
            </button>
            <button class="filter-btn px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap">
                报名中
            </button>
            <button class="filter-btn px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap">
                即将开始
            </button>
            <button class="filter-btn px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap">
                已结束
            </button>
        </div>
    </div>

    <!-- 活动列表 -->
    <main class="px-4 py-4 space-y-4">
        <!-- 活动卡片 1 - 报名中 -->
        <div class="activity-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                        企业数字化转型研讨会
                    </h3>
                    <span class="status-active px-2 py-1 rounded-full text-xs font-medium ml-2 whitespace-nowrap">
                        报名中
                    </span>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600 mb-3">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>2025年1月15日 14:00-17:00</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>北京市朝阳区国贸中心</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>企业管理者、IT负责人</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <span>已报名 <span class="text-blue-600 font-medium">28</span>/50人</span>
                    </div>
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 active:bg-blue-700">
                        立即报名
                    </button>
                </div>
            </div>
        </div>

        <!-- 活动卡片 2 - 已满员 -->
        <div class="activity-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden opacity-75">
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                        Python数据分析实战训练营
                    </h3>
                    <span class="status-full px-2 py-1 rounded-full text-xs font-medium ml-2 whitespace-nowrap">
                        已满员
                    </span>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600 mb-3">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>2025年1月20日 09:00-18:00</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>上海市浦东新区张江高科</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>数据分析师、程序员</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <span>已报名 <span class="text-red-600 font-medium">30</span>/30人</span>
                    </div>
                    <button class="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed" disabled>
                        已满员
                    </button>
                </div>
            </div>
        </div>

        <!-- 活动卡片 3 - 已结束 -->
        <div class="activity-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden opacity-60">
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                        区块链技术应用论坛
                    </h3>
                    <span class="status-ended px-2 py-1 rounded-full text-xs font-medium ml-2 whitespace-nowrap">
                        已结束
                    </span>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600 mb-3">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>2024年12月28日 13:30-17:30</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>深圳市南山区科技园</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>技术开发者、投资人</span>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <span>已报名 <span class="text-gray-600 font-medium">45</span>/50人</span>
                    </div>
                    <button class="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed" disabled>
                        已结束
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 加载更多 -->
    <div class="px-4 py-6 text-center">
        <button class="text-gray-500 text-sm">加载更多活动...</button>
    </div>

    <script>
        // 筛选功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有active状态
                document.querySelectorAll('.filter-btn').forEach(b => {
                    b.classList.remove('bg-blue-500', 'text-white');
                    b.classList.add('bg-gray-100', 'text-gray-600');
                });
                
                // 添加当前按钮active状态
                this.classList.remove('bg-gray-100', 'text-gray-600');
                this.classList.add('bg-blue-500', 'text-white');
            });
        });

        // 活动卡片点击事件
        document.querySelectorAll('.activity-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.closest('button')) {
                    // 跳转到活动详情页
                    window.location.href = 'activity-detail.html';
                }
            });
        });
    </script>
</body>
</html>