/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 活动管理API路由
 */

const express = require('express');
const router = express.Router();
const Activity = require('../models/Activity');
const Registration = require('../models/Registration');
const { authenticateToken } = require('../middleware/auth');

// 获取活动列表（公开接口）
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, keyword } = req.query;
    
    const filters = {};
    if (status !== undefined) filters.status = parseInt(status);
    if (keyword) filters.keyword = keyword;
    
    const activityModel = new Activity();
    const result = await activityModel.getActivities(filters, parseInt(page), parseInt(pageSize));
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取活动列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取活动列表失败'
    });
  }
});

// 获取活动详情（公开接口）
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const activityModel = new Activity();
    const activity = await activityModel.getActivityWithStats(id);
    
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }
    
    res.json({
      success: true,
      data: activity
    });
  } catch (error) {
    console.error('获取活动详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取活动详情失败'
    });
  }
});

// 创建活动（管理员接口）
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      title,
      description,
      target_audience,
      address,
      start_time,
      end_time,
      registration_start,
      registration_end,
      quota
    } = req.body;
    
    // 参数验证
    if (!title || !start_time || !end_time) {
      return res.status(400).json({
        success: false,
        message: '活动标题、开始时间和结束时间不能为空'
      });
    }
    
    const activityData = {
      title,
      description,
      target_audience,
      address,
      start_time,
      end_time,
      registration_start,
      registration_end,
      quota: quota || 0,
      status: 0,
      created_at: new Date()
    };
    
    const activityModel = new Activity();
    const activityId = await activityModel.create(activityData);
    
    res.json({
      success: true,
      message: '活动创建成功',
      data: { id: activityId }
    });
  } catch (error) {
    console.error('创建活动错误:', error);
    res.status(500).json({
      success: false,
      message: '创建活动失败'
    });
  }
});

module.exports = router;
