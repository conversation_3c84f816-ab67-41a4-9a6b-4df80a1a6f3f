# 在线报名系统需求文档

## 项目介绍

本系统是一套便捷的 H5 在线活动报名平台，支持移动端用户通过二维码进入报名页面，实现活动发布、报名登记、短信通知、统计导出等核心功能。系统旨在提升管理效率，简化流程，减少人工干预，适用于各类线下咨询、课程、会议等场景。

## 需求

### 需求 1：活动管理功能

**用户故事：** 作为管理员，我希望能够创建和管理活动信息，以便用户可以查看并报名参加活动。

#### 验收标准

1. 当管理员创建活动时，系统应当允许输入活动主题、内容介绍、活动对象、活动地址、开始时间、结束时间、报名上限等信息
2. 当管理员保存活动时，系统应当自动生成唯一的二维码链接
3. 当管理员发布活动时，系统应当将活动状态设置为"发布"并允许用户报名
4. 当活动达到报名上限时，系统应当自动关闭报名入口
5. 当管理员发布前需要预览时，系统应当提供活动预览功能，显示用户将看到的页面效果
6. 当管理员需要时，系统应当提供二维码下载和复制功能

### 需求 2：用户报名功能

**用户故事：** 作为用户，我希望能够通过扫描二维码快速报名活动，以便参加感兴趣的活动。

#### 验收标准

1. 当用户扫描二维码时，系统应当显示活动详情页面
2. 当用户点击"立即报名"时，系统应当要求输入手机号码
3. 当用户请求验证码时，系统应当通过云片网发送验证码短信
4. 当用户输入正确验证码时，系统应当完成报名并发送确认短信
5. 当同一手机号重复报名同一活动时，系统应当提示"您已报名成功"
6. 当验证码超过5分钟时，系统应当提示验证码已过期
7. 当用户每分钟请求验证码超过1次时，系统应当限制发送频率

### 需求 3：活动展示功能

**用户故事：** 作为用户，我希望能够浏览当前和近期的活动列表，以便选择感兴趣的活动。

#### 验收标准

1. 当用户访问活动列表页时，系统应当显示当前及近期活动
2. 当用户查看活动卡片时，系统应当显示标题、时间、对象、状态信息
3. 当用户点击活动时，系统应当显示详细的活动信息
4. 当用户已报名活动时，系统应当在详情页显示"您已报名成功，报名手机号：xxxx"
5. 当活动状态为结束或已满员时，系统应当隐藏报名按钮

### 需求 4：短信通知功能

**用户故事：** 作为系统，我需要通过云片网短信服务及时通知用户验证码和报名确认信息，以便用户能够完成报名流程。

#### 验收标准

1. 当用户请求验证码时，系统应当通过云片网API发送包含验证码的短信
2. 当用户报名成功时，系统应当通过云片网API发送包含活动详情的确认短信
3. 当短信发送失败时，系统应当记录失败状态并支持重发
4. 当管理员需要时，系统应当支持批量补发报名通知短信
5. 当使用短信模板时，系统应当正确替换模板参数（#topic#、#time#、#address#、#obj#）

### 需求 5：数据统计与导出功能

**用户故事：** 作为管理员，我希望能够查看报名统计数据并导出报名信息，以便进行活动管理和后续跟进。

#### 验收标准

1. 当管理员查看活动统计时，系统应当显示总报名人数、成功发送短信人数
2. 当管理员查看报名列表时，系统应当显示报名手机号并支持分页
3. 当管理员导出数据时，系统应当生成包含手机号、报名时间、短信状态的Excel文件
4. 当管理员选择特定活动时，系统应当支持按活动导出数据
5. 当管理员需要补发短信时，系统应当支持勾选手机号批量操作

### 需求 6：后台管理员认证功能

**用户故事：** 作为管理员，我希望通过账号密码登录后台管理系统，以便安全地管理活动和查看数据。

#### 验收标准

1. 当管理员访问后台时，系统应当显示登录页面要求输入账号和密码
2. 当管理员输入正确的账号密码时，系统应当允许访问后台管理功能
3. 当管理员输入错误的账号密码时，系统应当提示登录失败
4. 当管理员登录成功后，系统应当保持登录状态直到主动退出或会话过期
5. 当管理员长时间未操作时，系统应当自动退出登录状态

### 需求 7：UI原型设计功能

**用户故事：** 作为产品设计师，我希望创建高保真的UI原型，以便开发团队能够准确理解和实现用户界面。

#### 验收标准

1. 当设计H5用户端时，系统应当提供活动列表、详情页和报名流程的完整原型
2. 当设计管理后台时，系统应当提供登录、活动管理、数据统计的完整原型
3. 当创建交互演示时，系统应当展示完整的用户操作流程
4. 当输出设计规范时，系统应当包含组件库和设计标准文档
5. 当进行原型评审时，系统应当支持迭代优化和调整

### 需求 8：API接口设计功能

**用户故事：** 作为开发团队，我希望有完整的API接口设计文档，以便前后端能够并行开发和准确对接。

#### 验收标准

1. 当设计RESTful API时，系统应当定义完整的请求和响应格式
2. 当创建接口文档时，系统应当包含所有业务功能的API规范
3. 当提供Mock数据时，系统应当支持前端独立开发和测试
4. 当建立协作规范时，系统应当定义前后端数据交互协议
5. 当进行接口测试时，系统应当支持API功能验证和性能测试

### 需求 9：系统安全与风控功能

**用户故事：** 作为系统管理员，我希望系统具备必要的安全防护措施，以防止恶意攻击和滥用。

#### 验收标准

1. 当同一手机号每日请求验证码时，系统应当限制最大请求次数
2. 当验证码超过有效期时，系统应当拒绝验证请求
3. 当检测到异常请求频率时，系统应当实施IP限制
4. 当前端用户访问H5页面时，系统应当无需账号密码直接允许访问
5. 当MySQL 8数据库操作时，系统应当确保报名记录的唯一性约束

### 需求 10：单机部署功能

**用户故事：** 作为运维人员，我希望系统支持单机部署，以便在传统服务器环境中快速部署和维护。

#### 验收标准

1. 当部署系统时，系统应当支持在单台服务器上直接安装Node.js、MySQL、Redis
2. 当配置Web服务时，系统应当使用Nginx作为反向代理和静态文件服务
3. 当管理进程时，系统应当使用PM2等工具管理Node.js应用进程
4. 当创建部署脚本时，系统应当提供自动化的安装和配置脚本
5. 当进行服务管理时，系统应当支持服务启动、停止、重启和监控