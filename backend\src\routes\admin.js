/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 管理员认证路由
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const Admin = require('../models/Admin');
const { 
  generateToken, 
  authenticateToken, 
  blacklistToken,
  refreshToken 
} = require('../middleware/auth');

const router = express.Router();

// 登录限流 - 每15分钟最多5次登录尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    success: false,
    code: 4001,
    message: '登录尝试过于频繁，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * POST /api/admin/login
 * 管理员登录
 */
router.post('/login', loginLimiter, async (req, res) => {
  try {
    const { username, password } = req.body;

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        code: 1001,
        message: '用户名和密码不能为空'
      });
    }

    // 查找管理员
    const adminModel = new Admin();
    const admin = await adminModel.findByUsername(username);

    if (!admin) {
      return res.status(401).json({
        success: false,
        code: 3001,
        message: '用户名或密码错误'
      });
    }

    // 检查账户状态
    if (admin.status !== 1) {
      return res.status(401).json({
        success: false,
        code: 3007,
        message: '账户已被禁用'
      });
    }

    // 验证密码
    const isPasswordValid = await adminModel.verifyPassword(password, admin.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        code: 3001,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const tokenPayload = {
      adminId: admin.id,
      username: admin.username,
      name: admin.name,
      loginTime: new Date().toISOString()
    };
    const token = generateToken(tokenPayload);

    // 更新最后登录时间
    await adminModel.updateLastLogin(admin.id);

    // 返回登录成功信息
    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
          lastLoginAt: admin.last_login_at
        }
      }
    });

  } catch (error) {
    console.error('管理员登录错误:', error);
    res.status(500).json({
      success: false,
      code: 5000,
      message: '登录失败，请稍后重试'
    });
  }
});

/**
 * GET /api/admin/profile
 * 获取管理员信息
 */
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const adminModel = new Admin();
    const admin = await adminModel.findOne(
      { id: req.admin.id },
      'id, username, name, last_login_at, created_at'
    );

    if (!admin) {
      return res.status(404).json({
        success: false,
        code: 3003,
        message: '管理员信息不存在'
      });
    }

    res.json({
      success: true,
      message: '获取管理员信息成功',
      data: {
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
          lastLoginAt: admin.last_login_at,
          createdAt: admin.created_at
        }
      }
    });

  } catch (error) {
    console.error('获取管理员信息错误:', error);
    res.status(500).json({
      success: false,
      code: 5000,
      message: '获取管理员信息失败'
    });
  }
});

/**
 * POST /api/admin/logout
 * 管理员退出登录
 */
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // 将当前token加入黑名单
    await blacklistToken(req.token);

    res.json({
      success: true,
      message: '退出登录成功'
    });

  } catch (error) {
    console.error('退出登录错误:', error);
    res.status(500).json({
      success: false,
      code: 5000,
      message: '退出登录失败'
    });
  }
});

/**
 * POST /api/admin/refresh-token
 * 刷新访问令牌
 */
router.post('/refresh-token', authenticateToken, async (req, res) => {
  try {
    // 生成新的token载荷
    const tokenPayload = {
      adminId: req.admin.id,
      username: req.admin.username,
      name: req.admin.name,
      loginTime: new Date().toISOString()
    };

    // 刷新token
    const newToken = await refreshToken(req.token, tokenPayload);

    res.json({
      success: true,
      message: 'Token刷新成功',
      data: {
        token: newToken
      }
    });

  } catch (error) {
    console.error('Token刷新错误:', error);
    res.status(500).json({
      success: false,
      code: 5000,
      message: 'Token刷新失败'
    });
  }
});

/**
 * PUT /api/admin/password
 * 修改管理员密码
 */
router.put('/password', authenticateToken, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    // 参数验证
    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        code: 1001,
        message: '旧密码和新密码不能为空'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        code: 1002,
        message: '新密码长度不能少于6位'
      });
    }

    // 获取管理员信息
    const adminModel = new Admin();
    const admin = await adminModel.findOne({ id: req.admin.id });

    if (!admin) {
      return res.status(404).json({
        success: false,
        code: 3003,
        message: '管理员信息不存在'
      });
    }

    // 验证旧密码
    const isOldPasswordValid = await adminModel.verifyPassword(oldPassword, admin.password_hash);
    if (!isOldPasswordValid) {
      return res.status(401).json({
        success: false,
        code: 3008,
        message: '旧密码错误'
      });
    }

    // 更新密码
    await adminModel.changePassword(admin.id, newPassword);

    res.json({
      success: true,
      message: '密码修改成功'
    });

  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      code: 5000,
      message: '密码修改失败'
    });
  }
});

module.exports = router;
