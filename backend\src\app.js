/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 在线报名系统后端主应用入口
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入数据库配置
const { initDatabase } = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['http://localhost', 'http://127.0.0.1'] 
    : true,
  credentials: true
}));

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API限流
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  }
});
app.use('/api', limiter);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    success: true, 
    message: '服务运行正常',
    timestamp: new Date().toISOString()
  });
});

// API路由
app.use('/api/admin', require('./routes/admin'));
app.use('/api/activities', require('./routes/activities'));
app.use('/api/registrations', require('./routes/registrations'));
// app.use('/api/sms', require('./routes/sms')); // 待实现

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  const { code = 500, message = '服务器内部错误' } = error;
  
  console.error(`[${new Date().toISOString()}] ${req.method} ${req.path}`, error);
  
  res.status(code >= 1000 ? 400 : code).json({
    success: false,
    code,
    message,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库连接
    const dbConnected = await initDatabase();
    if (!dbConnected) {
      console.warn('⚠️ 数据库连接失败，服务器将在受限模式下启动');
    }

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`��� 服务器启动成功: http://localhost:${PORT}`);
      console.log(`��� 健康检查: http://localhost:${PORT}/health`);
      console.log(`��� 启动时间: ${new Date().toISOString()}`);
      if (!dbConnected) {
        console.log(`⚠️ 注意：数据库未连接，部分功能可能不可用`);
      }
    });
  } catch (error) {
    console.error('💥 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
startServer();

module.exports = app;
