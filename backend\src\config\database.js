/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 数据库连接配置
 */

const mysql = require("mysql2/promise");
const redis = require("redis");

// MySQL连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || "************",
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "9UfX8CrEMQ5Qk",
  database: process.env.DB_NAME || "registration_system",
  charset: "utf8mb4",
  timezone: "+08:00"
};

// 创建MySQL连接池
const mysqlPool = mysql.createPool({
  ...mysqlConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Redis连接配置
const redisConfig = {
  host: process.env.REDIS_HOST || "************",
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: 0
};

// 创建Redis客户端
const redisClient = redis.createClient(redisConfig);

// Redis连接事件处理
redisClient.on("connect", () => {
  console.log("✅ Redis连接成功");
});

redisClient.on("error", (err) => {
  console.error("❌ Redis连接错误:", err);
});

redisClient.on("ready", () => {
  console.log("��� Redis准备就绪");
});

// 初始化Redis连接
redisClient.connect().catch(err => {
  console.warn("⚠️ Redis连接失败，将在无缓存模式下运行:", err.message);
});

// 测试MySQL连接
async function testMysqlConnection() {
  try {
    const connection = await mysqlPool.getConnection();
    await connection.ping();
    connection.release();
    console.log("✅ MySQL连接成功");
    return true;
  } catch (error) {
    console.error("❌ MySQL连接失败:", error.message);
    return false;
  }
}

// 测试Redis连接
async function testRedisConnection() {
  try {
    await redisClient.ping();
    console.log("✅ Redis连接测试成功");
    return true;
  } catch (error) {
    console.error("❌ Redis连接测试失败:", error.message);
    return false;
  }
}

// 初始化数据库连接
async function initDatabase() {
  console.log("��� 正在初始化数据库连接...");
  
  const mysqlOk = await testMysqlConnection();
  const redisOk = await testRedisConnection();
  
  if (mysqlOk && redisOk) {
    console.log("✅ 数据库连接初始化完成");
    return true;
  } else if (mysqlOk) {
    console.warn("⚠️ MySQL连接成功，Redis连接失败，将在无缓存模式下运行");
    return true;
  } else {
    console.error("💥 MySQL连接失败，无法启动服务");
    return false;
  }
}

module.exports = {
  mysqlPool,
  redisClient,
  initDatabase,
  testMysqlConnection,
  testRedisConnection
};
