import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 1;
export type MSetArguments = Array<[RedisCommandArgument, RedisCommandArgument]> | Array<RedisCommandArgument> | Record<string, RedisCommandArgument>;
export declare function transformArguments(toSet: MSetArguments): RedisCommandArguments;
export declare function transformReply(): RedisCommandArgument;
