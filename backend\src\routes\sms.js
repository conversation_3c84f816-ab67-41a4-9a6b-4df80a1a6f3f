/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 短信服务API路由
 */

const express = require('express');
const router = express.Router();
const SmsService = require('../services/smsService');
const Activity = require('../models/Activity');
const { authenticateToken } = require('../middleware/auth');

// 发送验证码（公开接口）
router.post('/send-code', async (req, res) => {
  try {
    const { phone, eventId } = req.body;

    // 参数验证
    if (!phone || !eventId) {
      return res.status(400).json({
        success: false,
        message: '手机号和活动ID不能为空'
      });
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 检查活动是否存在且可以报名
    const activityModel = new Activity();
    const canRegisterResult = await activityModel.canRegister(eventId);
    
    if (!canRegisterResult.canRegister) {
      return res.status(400).json({
        success: false,
        message: canRegisterResult.reason
      });
    }

    // 发送验证码
    const smsService = new SmsService();
    const result = await smsService.sendVerificationCode(phone, eventId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    console.error('发送验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '发送验证码失败'
    });
  }
});

// 验证验证码（公开接口）
router.post('/verify-code', async (req, res) => {
  try {
    const { phone, code, eventId } = req.body;

    // 参数验证
    if (!phone || !code || !eventId) {
      return res.status(400).json({
        success: false,
        message: '手机号、验证码和活动ID不能为空'
      });
    }

    // 验证验证码
    const smsService = new SmsService();
    const result = await smsService.verifyCode(phone, code, eventId);

    if (result.valid) {
      res.json({
        success: true,
        message: '验证码验证成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.reason
      });
    }
  } catch (error) {
    console.error('验证验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '验证验证码失败'
    });
  }
});

// 发送报名通知（管理员接口）
router.post('/send-notification', authenticateToken, async (req, res) => {
  try {
    const { phone, activityTitle, startTime, address } = req.body;

    // 参数验证
    if (!phone || !activityTitle || !startTime) {
      return res.status(400).json({
        success: false,
        message: '手机号、活动标题和开始时间不能为空'
      });
    }

    // 发送报名通知
    const smsService = new SmsService();
    const result = await smsService.sendRegistrationNotification(phone, activityTitle, startTime, address);

    if (result.success) {
      res.json({
        success: true,
        message: '通知发送成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: '通知发送失败'
      });
    }
  } catch (error) {
    console.error('发送报名通知错误:', error);
    res.status(500).json({
      success: false,
      message: '发送报名通知失败'
    });
  }
});

// 批量补发短信（管理员接口）
router.post('/resend-batch', authenticateToken, async (req, res) => {
  try {
    const { eventId, phones } = req.body;

    if (!eventId || !phones || !Array.isArray(phones)) {
      return res.status(400).json({
        success: false,
        message: '活动ID和手机号列表不能为空'
      });
    }

    // 获取活动信息
    const activityModel = new Activity();
    const activity = await activityModel.findOne({ id: eventId });
    
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 批量发送短信
    const smsService = new SmsService();
    const results = [];
    
    for (const phone of phones) {
      const result = await smsService.sendRegistrationNotification(
        phone,
        activity.title,
        activity.start_time,
        activity.address
      );
      
      results.push({
        phone,
        success: result.success,
        message: result.success ? '发送成功' : result.error
      });
    }

    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    res.json({
      success: true,
      message: `批量发送完成，成功${successCount}条，失败${failCount}条`,
      data: {
        total: results.length,
        success: successCount,
        failed: failCount,
        details: results
      }
    });
  } catch (error) {
    console.error('批量补发短信错误:', error);
    res.status(500).json({
      success: false,
      message: '批量补发短信失败'
    });
  }
});

module.exports = router;
