# 在线报名系统设计文档

## 概述

本系统是一个基于H5的在线活动报名平台，采用前后端分离架构。系统包含管理后台和用户前端两个主要部分，支持活动发布、用户报名、短信通知、数据统计等核心功能。

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "用户端"
        A[H5前端页面] --> B[活动列表页]
        A --> C[活动详情页]
        A --> D[报名页面]
    end
    
    subgraph "管理端"
        E[后台管理系统] --> F[活动管理]
        E --> G[数据统计]
        E --> H[短信管理]
    end
    
    subgraph "后端服务"
        I[API网关] --> J[活动服务]
        I --> K[报名服务]
        I --> L[短信服务]
        I --> M[认证服务]
    end
    
    subgraph "数据层"
        N[MySQL 8数据库]
        O[Redis缓存]
    end
    
    subgraph "外部服务"
        P[云片网短信API]
    end
    
    A --> I
    E --> I
    J --> N
    K --> N
    L --> P
    K --> O
    L --> O
```

### 技术栈选择

**前端技术栈：**
- H5用户端：Vue 3 + Vite + TailwindCSS
- 管理后台：Vue 3 + Element Plus + Vite
- 移动端适配：响应式设计，兼容微信浏览器

**后端技术栈：**
- 框架：Node.js + Express
- 数据库：MySQL 8
- 缓存：Redis
- 短信服务：云片网API

## 组件和接口设计

### 前端组件架构

#### H5用户端组件

```mermaid
graph TD
    A[App.vue] --> B[ActivityList.vue]
    A --> C[ActivityDetail.vue]
    A --> D[Registration.vue]
    
    B --> E[ActivityCard.vue]
    C --> F[ActivityInfo.vue]
    C --> G[RegistrationButton.vue]
    D --> H[PhoneInput.vue]
    D --> I[VerificationCode.vue]
    D --> J[SuccessMessage.vue]
```

#### 管理后台组件

```mermaid
graph TD
    A[AdminApp.vue] --> B[Login.vue]
    A --> C[Dashboard.vue]
    A --> D[ActivityManagement.vue]
    A --> E[DataStatistics.vue]
    
    D --> F[ActivityForm.vue]
    D --> G[ActivityList.vue]
    D --> H[QRCodeGenerator.vue]
    D --> I[ActivityPreview.vue]
    
    E --> J[StatisticsChart.vue]
    E --> K[ExportData.vue]
    E --> L[SMSResend.vue]
```

### API接口设计

#### 活动管理接口

```javascript
// 获取活动列表
GET /api/activities
Query: { page, limit, status, keyword }
Response: { data: Activity[], total, page, limit }

// 获取活动详情
GET /api/activities/:id
Response: { data: Activity }

// 创建活动（管理员）
POST /api/admin/activities
Body: { title, description, address, startTime, endTime, quota, targetAudience }
Response: { data: Activity, qrCode }

// 更新活动（管理员）
PUT /api/admin/activities/:id
Body: { title, description, address, startTime, endTime, quota, targetAudience, status }

// 预览活动（管理员）
GET /api/admin/activities/:id/preview
Response: { data: Activity }
```

#### 报名相关接口

```javascript
// 发送验证码
POST /api/sms/verification
Body: { phone, eventId }
Response: { success: true, message }

// 提交报名
POST /api/registrations
Body: { phone, code, eventId }
Response: { success: true, registration: Registration }

// 检查报名状态
GET /api/registrations/check
Query: { phone, eventId }
Response: { registered: boolean, registration?: Registration }
```

#### 管理员接口

```javascript
// 管理员登录
POST /api/admin/login
Body: { username, password }
Response: { token, admin: Admin }

// 获取报名统计
GET /api/admin/activities/:id/statistics
Response: { totalRegistrations, smsSent, registrations: Registration[] }

// 导出报名数据
GET /api/admin/activities/:id/export
Response: Excel文件流

// 批量补发短信
POST /api/admin/sms/resend
Body: { phones: string[], eventId }
Response: { success: true, results: SMSResult[] }
```

## 数据模型设计

### 数据库表结构

#### 活动表 (activities)

```sql
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT '活动标题',
    description TEXT COMMENT '活动描述',
    target_audience VARCHAR(255) COMMENT '目标对象',
    address VARCHAR(500) COMMENT '活动地址',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    registration_start DATETIME COMMENT '报名开始时间',
    registration_end DATETIME COMMENT '报名结束时间',
    quota INT DEFAULT 0 COMMENT '报名上限',
    status TINYINT DEFAULT 0 COMMENT '状态：0草稿，1发布，2结束',
    qr_code_url VARCHAR(500) COMMENT '二维码URL',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

#### 报名记录表 (registrations)

```sql
CREATE TABLE registrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    event_id BIGINT NOT NULL,
    phone VARCHAR(20) NOT NULL COMMENT '报名手机号',
    sms_status TINYINT DEFAULT 0 COMMENT '短信状态：0待发送，1成功，2失败',
    sms_sent_at DATETIME COMMENT '短信发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES activities(id),
    UNIQUE KEY uk_event_phone (event_id, phone),
    INDEX idx_event_id (event_id),
    INDEX idx_phone (phone)
);
```

#### 验证码表 (sms_codes)

```sql
CREATE TABLE sms_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    code VARCHAR(10) NOT NULL,
    event_id BIGINT,
    expired_at DATETIME NOT NULL,
    used TINYINT DEFAULT 0 COMMENT '是否已使用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_phone_event (phone, event_id),
    INDEX idx_expired (expired_at)
);
```

#### 管理员表 (admins)

```sql
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) COMMENT '管理员姓名',
    status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1启用',
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 短信发送记录表 (sms_logs)

```sql
CREATE TABLE sms_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    type TINYINT NOT NULL COMMENT '类型：1验证码，2报名通知',
    content TEXT COMMENT '短信内容',
    status TINYINT DEFAULT 0 COMMENT '状态：0发送中，1成功，2失败',
    yunpian_response TEXT COMMENT '云片网响应',
    event_id BIGINT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_event_id (event_id),
    INDEX idx_created_at (created_at)
);
```

## 错误处理

### 错误码定义

```javascript
const ERROR_CODES = {
    // 通用错误
    INVALID_PARAMS: { code: 400, message: '参数错误' },
    UNAUTHORIZED: { code: 401, message: '未授权访问' },
    FORBIDDEN: { code: 403, message: '禁止访问' },
    NOT_FOUND: { code: 404, message: '资源不存在' },
    
    // 业务错误
    ACTIVITY_NOT_FOUND: { code: 1001, message: '活动不存在' },
    ACTIVITY_ENDED: { code: 1002, message: '活动已结束' },
    REGISTRATION_FULL: { code: 1003, message: '报名已满' },
    ALREADY_REGISTERED: { code: 1004, message: '您已报名成功' },
    
    // 验证码错误
    SMS_SEND_FAILED: { code: 2001, message: '短信发送失败' },
    SMS_LIMIT_EXCEEDED: { code: 2002, message: '短信发送次数超限' },
    INVALID_CODE: { code: 2003, message: '验证码错误' },
    CODE_EXPIRED: { code: 2004, message: '验证码已过期' },
    
    // 登录错误
    INVALID_CREDENTIALS: { code: 3001, message: '用户名或密码错误' },
    TOKEN_EXPIRED: { code: 3002, message: '登录已过期' }
};
```

### 错误处理中间件

```javascript
// 全局错误处理中间件
app.use((error, req, res, next) => {
    const { code = 500, message = '服务器内部错误' } = error;
    
    // 记录错误日志
    console.error(`[${new Date().toISOString()}] ${req.method} ${req.path}`, error);
    
    res.status(code >= 1000 ? 400 : code).json({
        success: false,
        code,
        message,
        timestamp: new Date().toISOString()
    });
});
```

## 测试策略

### 单元测试

**前端测试：**
- 使用 Vitest + Vue Test Utils
- 组件渲染测试
- 用户交互测试
- API调用模拟测试

**后端测试：**
- 使用 Jest + Supertest
- API接口测试
- 数据库操作测试
- 短信服务模拟测试

### 集成测试

**端到端测试场景：**
1. 管理员创建活动流程
2. 用户扫码报名完整流程
3. 短信发送和验证流程
4. 数据统计和导出功能

**测试工具：**
- Playwright 进行E2E测试
- 模拟云片网API响应
- 数据库测试数据准备和清理

### 性能测试

**关键指标：**
- H5页面加载时间 < 2秒
- API响应时间 < 500ms
- 并发报名处理能力
- 短信发送成功率 > 95%

**测试工具：**
- Artillery 进行负载测试
- 监控数据库查询性能
- Redis缓存命中率监控

## 部署架构设计

### 单机部署架构

```mermaid
graph TB
    subgraph "单台服务器"
        subgraph "Web层"
            A[Nginx反向代理] --> B[静态文件服务]
            A --> C[API请求转发]
        end
        
        subgraph "应用层"
            D[PM2进程管理] --> E[Node.js应用实例1]
            D --> F[Node.js应用实例2]
            D --> G[Node.js应用实例N]
        end
        
        subgraph "数据层"
            H[MySQL 8数据库]
            I[Redis缓存服务]
        end
        
        subgraph "日志监控"
            J[应用日志]
            K[访问日志]
            L[错误日志]
        end
    end
    
    subgraph "外部服务"
        M[云片网短信API]
    end
    
    A --> D
    E --> H
    E --> I
    F --> H
    F --> I
    G --> H
    G --> I
    E --> M
    F --> M
    G --> M
```

### 部署环境配置

**服务器要求：**
- 操作系统：CentOS 7+ / Ubuntu 18.04+
- CPU：2核心以上
- 内存：4GB以上
- 硬盘：50GB以上
- 网络：公网IP，支持80/443端口

**软件环境：**
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.18+
- PM2进程管理器

### 部署目录结构

```
/opt/registration-system/
├── frontend/                 # 前端构建文件
│   ├── h5/                  # H5用户端
│   └── admin/               # 管理后台
├── backend/                 # 后端应用
│   ├── src/                 # 源代码
│   ├── config/              # 配置文件
│   └── logs/                # 应用日志
├── scripts/                 # 部署脚本
│   ├── install.sh           # 环境安装脚本
│   ├── deploy.sh            # 部署脚本
│   └── backup.sh            # 备份脚本
├── nginx/                   # Nginx配置
│   └── registration.conf    # 站点配置
└── database/                # 数据库脚本
    ├── schema.sql           # 表结构
    └── init-data.sql        # 初始数据
```

### 服务配置

**Nginx配置示例：**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # H5前端
    location / {
        root /opt/registration-system/frontend/h5;
        try_files $uri $uri/ /index.html;
    }
    
    # 管理后台
    location /admin {
        root /opt/registration-system/frontend;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API接口
    location /api {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

**PM2配置示例：**
```javascript
module.exports = {
    apps: [{
        name: 'registration-api',
        script: './src/app.js',
        instances: 2,
        exec_mode: 'cluster',
        env: {
            NODE_ENV: 'production',
            PORT: 3000
        },
        log_file: './logs/combined.log',
        out_file: './logs/out.log',
        error_file: './logs/error.log',
        log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }]
};
```

## 安全考虑

### 数据安全

1. **密码安全：** 管理员密码使用bcrypt加密存储
2. **SQL注入防护：** 使用参数化查询
3. **XSS防护：** 前端输入验证和输出转义
4. **CSRF防护：** 使用CSRF token

### 接口安全

1. **频率限制：** 使用Redis实现接口调用频率限制
2. **参数验证：** 严格验证所有输入参数
3. **权限控制：** JWT token验证管理员身份
4. **HTTPS：** 生产环境强制使用HTTPS

### 短信安全

1. **验证码限制：** 每手机号每日最多10次验证码
2. **IP限制：** 同一IP每分钟最多请求5次验证码
3. **验证码复杂度：** 6位数字验证码，5分钟有效期
4. **防刷机制：** 记录异常请求并实施黑名单机制

### 部署安全

1. **防火墙配置：** 只开放必要端口（80, 443, 22）
2. **数据库安全：** MySQL只允许本地连接，设置强密码
3. **文件权限：** 设置适当的文件和目录权限
4. **定期备份：** 自动备份数据库和重要配置文件