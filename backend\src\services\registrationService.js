/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名业务服务类
 */

const Registration = require('../models/Registration');
const Activity = require('../models/Activity');
const SmsService = require('./smsService');
const { mysqlPool } = require('../config/database');

class RegistrationService {
  constructor() {
    this.registrationModel = new Registration();
    this.activityModel = new Activity();
    this.smsService = new SmsService();
  }

  // 完整的报名流程
  async processRegistration(eventId, phone, code) {
    const connection = await mysqlPool.getConnection();
    
    try {
      await connection.beginTransaction();

      // 1. 验证验证码
      const codeVerification = await this.smsService.verifyCode(phone, code, eventId);
      if (!codeVerification.valid) {
        throw new Error(codeVerification.reason);
      }

      // 2. 检查活动状态
      const canRegisterResult = await this.activityModel.canRegister(eventId);
      if (!canRegisterResult.canRegister) {
        throw new Error(canRegisterResult.reason);
      }

      // 3. 检查重复报名
      const existingRegistration = await this.registrationModel.checkRegistration(eventId, phone);
      if (existingRegistration) {
        throw new Error('您已经报名过该活动');
      }

      // 4. 检查报名人数限制（加锁防止超额）
      const activity = canRegisterResult.activity;
      if (activity.quota > 0) {
        const currentCount = await this.getCurrentRegistrationCount(eventId, connection);
        if (currentCount >= activity.quota) {
          throw new Error('报名已满');
        }
      }

      // 5. 创建报名记录
      const registrationId = await this.createRegistrationWithConnection(eventId, phone, connection);

      // 6. 发送报名成功通知
      let smsStatus = 0;
      let smsError = null;
      
      try {
        const smsResult = await this.smsService.sendRegistrationNotification(
          phone,
          activity.title,
          this.formatDateTime(activity.start_time),
          activity.address
        );
        
        smsStatus = smsResult.success ? 1 : 2;
        if (!smsResult.success) {
          smsError = smsResult.error;
        }
      } catch (error) {
        console.error('发送报名通知失败:', error);
        smsStatus = 2;
        smsError = error.message;
      }

      // 7. 更新短信发送状态
      await this.updateSmsStatusWithConnection(registrationId, smsStatus, connection);

      await connection.commit();

      return {
        success: true,
        data: {
          registrationId,
          smsStatus,
          smsError
        }
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 获取当前报名人数（带连接）
  async getCurrentRegistrationCount(eventId, connection) {
    const sql = 'SELECT COUNT(*) as count FROM registrations WHERE event_id = ?';
    const [rows] = await connection.execute(sql, [eventId]);
    return rows[0].count;
  }

  // 创建报名记录（带连接）
  async createRegistrationWithConnection(eventId, phone, connection) {
    const sql = `
      INSERT INTO registrations (event_id, phone, sms_status, created_at) 
      VALUES (?, ?, 0, NOW())
    `;
    const [result] = await connection.execute(sql, [eventId, phone]);
    return result.insertId;
  }

  // 更新短信状态（带连接）
  async updateSmsStatusWithConnection(registrationId, status, connection) {
    const sql = `
      UPDATE registrations 
      SET sms_status = ?, sms_sent_at = NOW() 
      WHERE id = ?
    `;
    await connection.execute(sql, [status, registrationId]);
  }

  // 格式化日期时间
  formatDateTime(dateTime) {
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // 获取报名详细信息
  async getRegistrationDetails(eventId, phone) {
    const registration = await this.registrationModel.checkRegistration(eventId, phone);
    if (!registration) {
      return { registered: false };
    }

    const activity = await this.activityModel.findOne({ id: eventId });
    
    return {
      registered: true,
      registration: {
        ...registration,
        activity: activity ? {
          title: activity.title,
          start_time: activity.start_time,
          address: activity.address
        } : null
      }
    };
  }

  // 批量补发短信
  async batchResendSms(eventId, phones) {
    const activity = await this.activityModel.findOne({ id: eventId });
    if (!activity) {
      throw new Error('活动不存在');
    }

    const results = [];
    
    for (const phone of phones) {
      try {
        const smsResult = await this.smsService.sendRegistrationNotification(
          phone,
          activity.title,
          this.formatDateTime(activity.start_time),
          activity.address
        );

        // 更新数据库中的短信状态
        const registration = await this.registrationModel.checkRegistration(eventId, phone);
        if (registration) {
          await this.registrationModel.updateSmsStatus(
            registration.id, 
            smsResult.success ? 1 : 2,
            new Date()
          );
        }

        results.push({
          phone,
          success: smsResult.success,
          message: smsResult.success ? '发送成功' : smsResult.error
        });
      } catch (error) {
        results.push({
          phone,
          success: false,
          message: error.message
        });
      }
    }

    return results;
  }
}

module.exports = RegistrationService;
