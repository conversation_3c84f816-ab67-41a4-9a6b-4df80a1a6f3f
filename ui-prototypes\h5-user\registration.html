<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动报名 - 在线报名系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 微信浏览器兼容性 */
        body { -webkit-text-size-adjust: 100%; }
        .back-btn:active { transform: scale(0.95); }
        .submit-btn:active { transform: scale(0.98); }
        
        /* 输入框焦点样式 */
        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        /* 验证码按钮样式 */
        .code-btn:disabled {
            background-color: #d1d5db;
            color: #6b7280;
            cursor: not-allowed;
        }
        
        /* 错误提示样式 */
        .error-message {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
        <div class="flex items-center px-4 py-3">
            <button class="back-btn mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors" onclick="history.back()">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-900">活动报名</h1>
        </div>
    </header>

    <!-- 活动信息卡片 -->
    <div class="bg-white mx-4 mt-4 rounded-lg shadow-sm border border-gray-200 p-4">
        <h2 class="text-lg font-semibold text-gray-900 mb-2">企业数字化转型研讨会</h2>
        <div class="space-y-1 text-sm text-gray-600">
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span>2025年1月15日 14:00-17:00</span>
            </div>
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>北京市朝阳区国贸中心</span>
            </div>
        </div>
    </div>

    <!-- 报名表单 -->
    <div class="bg-white mx-4 mt-4 rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">填写报名信息</h3>
        
        <form id="registration-form" class="space-y-6">
            <!-- 手机号输入 -->
            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                    手机号码 <span class="text-red-500">*</span>
                </label>
                <input 
                    type="tel" 
                    id="phone" 
                    name="phone"
                    class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all"
                    placeholder="请输入您的手机号码"
                    maxlength="11"
                    required
                >
                <div id="phone-error" class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>

            <!-- 验证码输入 -->
            <div>
                <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-2">
                    验证码 <span class="text-red-500">*</span>
                </label>
                <div class="flex space-x-3">
                    <input 
                        type="text" 
                        id="verification-code" 
                        name="code"
                        class="form-input flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all"
                        placeholder="请输入验证码"
                        maxlength="6"
                        required
                    >
                    <button 
                        type="button" 
                        id="send-code-btn"
                        class="code-btn px-6 py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors whitespace-nowrap"
                        onclick="sendVerificationCode()"
                    >
                        获取验证码
                    </button>
                </div>
                <div id="code-error" class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>

            <!-- 提交按钮 -->
            <button 
                type="submit" 
                id="submit-btn"
                class="submit-btn w-full bg-blue-500 text-white py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 active:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
                确认报名
            </button>
        </form>
    </div>

    <!-- 温馨提示 -->
    <div class="mx-4 mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="text-sm text-blue-700">
                <div class="font-medium mb-1">温馨提示</div>
                <ul class="space-y-1 text-blue-600">
                    <li>• 验证码有效期为5分钟，请及时输入</li>
                    <li>• 每个手机号只能报名一次</li>
                    <li>• 报名成功后将收到确认短信</li>
                    <li>• 如有疑问请联系客服：400-123-4567</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 成功提示模态框 -->
    <div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center px-4 z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">报名成功！</h3>
                <p class="text-gray-600 text-sm mb-6">
                    您已成功报名参加活动，确认短信已发送至您的手机，请注意查收。
                </p>
                <button 
                    class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors"
                    onclick="closeSuccessModal()"
                >
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        let countdown = 0;
        let countdownTimer = null;

        // 手机号验证
        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        // 验证码验证
        function validateCode(code) {
            return /^\d{6}$/.test(code);
        }

        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
            setTimeout(() => {
                errorElement.classList.add('hidden');
            }, 3000);
        }

        // 发送验证码
        function sendVerificationCode() {
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();
            const sendBtn = document.getElementById('send-code-btn');

            // 验证手机号
            if (!phone) {
                showError('phone-error', '请输入手机号码');
                phoneInput.focus();
                return;
            }

            if (!validatePhone(phone)) {
                showError('phone-error', '请输入正确的手机号码');
                phoneInput.focus();
                return;
            }

            // 模拟发送验证码
            sendBtn.disabled = true;
            countdown = 60;
            
            // 更新按钮文本
            function updateCountdown() {
                if (countdown > 0) {
                    sendBtn.textContent = `${countdown}秒后重发`;
                    countdown--;
                    countdownTimer = setTimeout(updateCountdown, 1000);
                } else {
                    sendBtn.textContent = '获取验证码';
                    sendBtn.disabled = false;
                }
            }
            
            updateCountdown();

            // 模拟API调用
            setTimeout(() => {
                alert('验证码已发送到您的手机，请注意查收');
            }, 500);
        }

        // 提交报名表单
        document.getElementById('registration-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            const code = document.getElementById('verification-code').value.trim();
            const submitBtn = document.getElementById('submit-btn');

            // 验证手机号
            if (!validatePhone(phone)) {
                showError('phone-error', '请输入正确的手机号码');
                return;
            }

            // 验证验证码
            if (!validateCode(code)) {
                showError('code-error', '请输入6位数字验证码');
                return;
            }

            // 提交报名
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            // 模拟API调用
            setTimeout(() => {
                // 模拟成功响应
                showSuccessModal();
                submitBtn.disabled = false;
                submitBtn.textContent = '确认报名';
            }, 1500);
        });

        // 显示成功模态框
        function showSuccessModal() {
            document.getElementById('success-modal').classList.remove('hidden');
        }

        // 关闭成功模态框
        function closeSuccessModal() {
            document.getElementById('success-modal').classList.add('hidden');
            // 返回活动详情页
            history.back();
        }

        // 手机号输入格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 验证码输入格式化
        document.getElementById('verification-code').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) {
                value = value.slice(0, 6);
            }
            e.target.value = value;
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (countdownTimer) {
                clearTimeout(countdownTimer);
            }
        });
    </script>
</body>
</html>