<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动详情 - 在线报名系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 微信浏览器兼容性 */
        body { -webkit-text-size-adjust: 100%; }
        .back-btn:active { transform: scale(0.95); }
        .register-btn:active { transform: scale(0.98); }
        
        /* 状态样式 */
        .status-active { @apply bg-green-100 text-green-600; }
        .status-full { @apply bg-red-100 text-red-600; }
        .status-ended { @apply bg-gray-100 text-gray-500; }
        
        /* 已报名状态样式 */
        .registered-notice {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
        <div class="flex items-center px-4 py-3">
            <button class="back-btn mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors" onclick="history.back()">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-900">活动详情</h1>
        </div>
    </header>

    <!-- 活动主要信息 -->
    <div class="bg-white border-b border-gray-200">
        <div class="px-4 py-6">
            <div class="flex justify-between items-start mb-4">
                <h1 class="text-xl font-bold text-gray-900 leading-tight pr-4">
                    企业数字化转型研讨会
                </h1>
                <span class="status-active px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap">
                    报名中
                </span>
            </div>
            
            <div class="space-y-3 text-gray-600">
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">活动时间</div>
                        <div class="text-sm">2025年1月15日 14:00-17:00</div>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">活动地址</div>
                        <div class="text-sm">北京市朝阳区国贸中心A座15楼会议室</div>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">参与对象</div>
                        <div class="text-sm">企业管理者、IT负责人、数字化转型相关从业者</div>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">报名情况</div>
                        <div class="text-sm">已报名 <span class="text-blue-600 font-semibold">28</span>/50人</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动详细介绍 -->
    <div class="bg-white mt-2">
        <div class="px-4 py-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">活动介绍</h2>
            <div class="prose prose-sm text-gray-600 leading-relaxed">
                <p class="mb-4">
                    随着数字化浪潮的推进，企业数字化转型已成为时代发展的必然趋势。本次研讨会将邀请行业专家、成功企业代表，共同探讨数字化转型的实践路径与成功经验。
                </p>
                
                <h3 class="text-base font-semibold text-gray-900 mb-2">议程安排</h3>
                <div class="space-y-3 mb-4">
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">14:00</span>
                        <span>签到入场</span>
                    </div>
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">14:30</span>
                        <span>开场致辞及主题演讲：数字化转型的战略思考</span>
                    </div>
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">15:30</span>
                        <span>案例分享：传统制造业的数字化转型实践</span>
                    </div>
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">16:00</span>
                        <span>茶歇交流</span>
                    </div>
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">16:20</span>
                        <span>圆桌讨论：数字化转型中的挑战与机遇</span>
                    </div>
                    <div class="flex">
                        <span class="text-blue-600 font-medium w-20 flex-shrink-0">17:00</span>
                        <span>总结发言及合影留念</span>
                    </div>
                </div>
                
                <h3 class="text-base font-semibold text-gray-900 mb-2">您将收获</h3>
                <ul class="list-disc list-inside space-y-1 text-sm">
                    <li>了解数字化转型的最新趋势和发展方向</li>
                    <li>学习成功企业的数字化转型实践经验</li>
                    <li>建立行业内的人脉网络和合作机会</li>
                    <li>获得专业的数字化转型指导建议</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 已报名提示（隐藏状态，通过JS控制显示） -->
    <div id="registered-notice" class="registered-notice text-white mx-4 my-4 p-4 rounded-lg hidden">
        <div class="flex items-center">
            <svg class="w-6 h-6 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <div class="font-semibold">您已报名成功</div>
                <div class="text-sm opacity-90">报名手机号：138****5678</div>
            </div>
        </div>
    </div>

    <!-- 底部报名按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 safe-area-pb">
        <button id="register-btn" class="register-btn w-full bg-blue-500 text-white py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 active:bg-blue-700 transition-colors" onclick="goToRegistration()">
            立即报名
        </button>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        // 模拟用户报名状态检查
        function checkRegistrationStatus() {
            // 这里应该调用API检查用户是否已报名
            // 模拟已报名状态
            const isRegistered = false; // 改为true可以看到已报名状态
            
            if (isRegistered) {
                document.getElementById('registered-notice').classList.remove('hidden');
                const registerBtn = document.getElementById('register-btn');
                registerBtn.textContent = '您已报名成功';
                registerBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600', 'active:bg-blue-700');
                registerBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
                registerBtn.disabled = true;
                registerBtn.onclick = null;
            }
        }

        // 跳转到报名页面
        function goToRegistration() {
            window.location.href = 'registration.html';
        }

        // 页面加载时检查报名状态
        document.addEventListener('DOMContentLoaded', function() {
            checkRegistrationStatus();
        });

        // 处理iOS Safari的安全区域
        if (window.CSS && CSS.supports('padding-bottom', 'env(safe-area-inset-bottom)')) {
            document.querySelector('.safe-area-pb').style.paddingBottom = 'calc(1rem + env(safe-area-inset-bottom))';
        }
    </script>
</body>
</html>