/**
 * 数据库连接测试脚本
 */

const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    console.log('正在测试数据库连接...');
    
    // 先连接到MySQL服务器（不指定数据库）
    const connection = await mysql.createConnection({
      host: '************',
      port: 3306,
      user: 'root',
      password: '9UfX8CrEMQ5Qk'
    });
    
    console.log('✅ MySQL服务器连接成功');
    
    // 检查数据库是否存在
    const [databases] = await connection.execute('SHOW DATABASES LIKE "registration_system"');
    
    if (databases.length === 0) {
      console.log('📝 数据库不存在，正在创建...');
      await connection.execute('CREATE DATABASE registration_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
      console.log('✅ 数据库创建成功');
    } else {
      console.log('✅ 数据库已存在');
    }
    
    // 切换到目标数据库
    await connection.execute('USE registration_system');
    
    // 检查表是否存在
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 现有表:', tables.map(t => Object.values(t)[0]));
    
    await connection.end();
    console.log('✅ 数据库测试完成');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

testConnection();
