<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动管理 - 在线报名系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        /* 模态框样式 */
        .modal {
            backdrop-filter: blur(4px);
        }
        
        /* 表单样式 */
        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        /* 状态标签样式 */
        .status-draft { @apply bg-gray-100 text-gray-600; }
        .status-active { @apply bg-green-100 text-green-600; }
        .status-full { @apply bg-red-100 text-red-600; }
        .status-ended { @apply bg-gray-100 text-gray-500; }
        
        /* 二维码容器样式 */
        .qr-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center">
                <button onclick="history.back()" class="mr-4 p-2 rounded-md hover:bg-gray-100">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <h1 class="text-xl font-semibold text-gray-900">活动管理</h1>
            </div>
            <button onclick="showCreateModal()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                创建活动
            </button>
        </div>
    </header>

    <!-- 筛选和搜索栏 -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div class="flex space-x-4">
                <select class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">全部状态</option>
                    <option value="draft">草稿</option>
                    <option value="active">报名中</option>
                    <option value="full">已满员</option>
                    <option value="ended">已结束</option>
                </select>
                <input type="date" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="flex space-x-2">
                <input type="text" placeholder="搜索活动名称..." class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 活动列表 -->
    <main class="p-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报名情况</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">企业数字化转型研讨会</div>
                                    <div class="text-sm text-gray-500">北京市朝阳区国贸中心</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">2025-01-15</div>
                                <div class="text-sm text-gray-500">14:00-17:00</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">28/50人</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 56%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-active px-2 py-1 text-xs font-medium rounded-full">报名中</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button onclick="showQRCode('企业数字化转型研讨会')" class="text-blue-600 hover:text-blue-800 text-sm">二维码</button>
                                    <button onclick="previewActivity(1)" class="text-green-600 hover:text-green-800 text-sm">预览</button>
                                    <button onclick="editActivity(1)" class="text-yellow-600 hover:text-yellow-800 text-sm">编辑</button>
                                    <button onclick="viewStats(1)" class="text-purple-600 hover:text-purple-800 text-sm">统计</button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Python数据分析实战训练营</div>
                                    <div class="text-sm text-gray-500">上海市浦东新区张江高科</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">2025-01-20</div>
                                <div class="text-sm text-gray-500">09:00-18:00</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">30/30人</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-red-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-full px-2 py-1 text-xs font-medium rounded-full">已满员</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button onclick="showQRCode('Python数据分析实战训练营')" class="text-blue-600 hover:text-blue-800 text-sm">二维码</button>
                                    <button onclick="previewActivity(2)" class="text-green-600 hover:text-green-800 text-sm">预览</button>
                                    <button onclick="editActivity(2)" class="text-yellow-600 hover:text-yellow-800 text-sm">编辑</button>
                                    <button onclick="viewStats(2)" class="text-purple-600 hover:text-purple-800 text-sm">统计</button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">区块链技术应用论坛</div>
                                    <div class="text-sm text-gray-500">深圳市南山区科技园</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">2024-12-28</div>
                                <div class="text-sm text-gray-500">13:30-17:30</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">45/50人</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-gray-600 h-2 rounded-full" style="width: 90%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-ended px-2 py-1 text-xs font-medium rounded-full">已结束</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button onclick="showQRCode('区块链技术应用论坛')" class="text-blue-600 hover:text-blue-800 text-sm">二维码</button>
                                    <button onclick="previewActivity(3)" class="text-green-600 hover:text-green-800 text-sm">预览</button>
                                    <button onclick="viewStats(3)" class="text-purple-600 hover:text-purple-800 text-sm">统计</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
            </div>
            <div class="flex space-x-2">
                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>
                    上一页
                </button>
                <button class="px-3 py-2 bg-blue-500 text-white rounded-lg text-sm">1</button>
                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>
                    下一页
                </button>
            </div>
        </div>
    </main>

    <!-- 创建/编辑活动模态框 -->
    <div id="activity-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center px-4 z-50 hidden">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 id="modal-title" class="text-lg font-semibold text-gray-900">创建活动</h3>
                    <button onclick="closeActivityModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <form id="activity-form" class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">活动标题 <span class="text-red-500">*</span></label>
                        <input type="text" name="title" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" placeholder="请输入活动标题" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">开始时间 <span class="text-red-500">*</span></label>
                        <input type="datetime-local" name="startTime" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">结束时间 <span class="text-red-500">*</span></label>
                        <input type="datetime-local" name="endTime" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" required>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">活动地址 <span class="text-red-500">*</span></label>
                        <input type="text" name="address" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" placeholder="请输入活动地址" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">参与对象</label>
                        <input type="text" name="targetAudience" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" placeholder="请输入参与对象">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">报名上限 <span class="text-red-500">*</span></label>
                        <input type="number" name="quota" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" placeholder="请输入报名上限" min="1" required>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">活动介绍</label>
                        <textarea name="description" rows="6" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all" placeholder="请输入活动详细介绍"></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeActivityModal()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                    <button type="button" onclick="saveAsDraft()" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                        保存草稿
                    </button>
                    <button type="submit" class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        发布活动
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 二维码模态框 -->
    <div id="qr-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center px-4 z-50 hidden">
        <div class="bg-white rounded-lg max-w-md w-full">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">活动二维码</h3>
                    <button onclick="closeQRModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="p-6 text-center">
                <div id="qr-title" class="text-lg font-medium text-gray-900 mb-4"></div>
                <div class="qr-container p-6 rounded-lg inline-block mb-4">
                    <canvas id="qr-canvas" class="bg-white rounded"></canvas>
                </div>
                <div class="space-y-3">
                    <button onclick="downloadQR()" class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition-colors">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4"></path>
                        </svg>
                        下载二维码
                    </button>
                    <button onclick="copyQRLink()" class="w-full bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        复制链接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentActivityId = null;
        let currentQRData = null;

        // 显示创建活动模态框
        function showCreateModal() {
            currentActivityId = null;
            document.getElementById('modal-title').textContent = '创建活动';
            document.getElementById('activity-form').reset();
            document.getElementById('activity-modal').classList.remove('hidden');
        }

        // 显示编辑活动模态框
        function editActivity(id) {
            currentActivityId = id;
            document.getElementById('modal-title').textContent = '编辑活动';
            // 这里应该加载活动数据并填充表单
            document.getElementById('activity-modal').classList.remove('hidden');
        }

        // 关闭活动模态框
        function closeActivityModal() {
            document.getElementById('activity-modal').classList.add('hidden');
        }

        // 显示二维码
        function showQRCode(activityTitle) {
            const url = `https://example.com/activity/${encodeURIComponent(activityTitle)}`;
            currentQRData = { title: activityTitle, url: url };
            
            document.getElementById('qr-title').textContent = activityTitle;
            
            // 生成二维码
            const canvas = document.getElementById('qr-canvas');
            QRCode.toCanvas(canvas, url, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            });
            
            document.getElementById('qr-modal').classList.remove('hidden');
        }

        // 关闭二维码模态框
        function closeQRModal() {
            document.getElementById('qr-modal').classList.add('hidden');
        }

        // 下载二维码
        function downloadQR() {
            if (!currentQRData) return;
            
            const canvas = document.getElementById('qr-canvas');
            const link = document.createElement('a');
            link.download = `${currentQRData.title}-二维码.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        // 复制二维码链接
        function copyQRLink() {
            if (!currentQRData) return;
            
            navigator.clipboard.writeText(currentQRData.url).then(() => {
                alert('链接已复制到剪贴板');
            });
        }

        // 预览活动
        function previewActivity(id) {
            // 打开新窗口预览活动
            window.open('../h5-user/activity-detail.html', '_blank');
        }

        // 查看统计
        function viewStats(id) {
            alert('跳转到活动统计页面');
        }

        // 保存草稿
        function saveAsDraft() {
            const formData = new FormData(document.getElementById('activity-form'));
            // 这里应该调用API保存草稿
            alert('活动已保存为草稿');
            closeActivityModal();
        }

        // 表单提交
        document.getElementById('activity-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // 基本验证
            if (!data.title || !data.startTime || !data.endTime || !data.address || !data.quota) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 时间验证
            if (new Date(data.startTime) >= new Date(data.endTime)) {
                alert('结束时间必须晚于开始时间');
                return;
            }
            
            // 这里应该调用API创建/更新活动
            console.log('活动数据:', data);
            alert(currentActivityId ? '活动更新成功' : '活动创建成功');
            closeActivityModal();
            
            // 刷新页面数据
            location.reload();
        });

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                window.location.href = 'login.html';
                return;
            }
        });
    </script>
</body>
</html>