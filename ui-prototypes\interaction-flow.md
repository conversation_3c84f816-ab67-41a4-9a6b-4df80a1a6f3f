# 在线报名系统交互流程图

## 用户端交互流程

### 1. 用户报名完整流程

```mermaid
graph TD
    A[用户扫描二维码] --> B[进入活动列表页]
    B --> C[浏览活动信息]
    C --> D[点击感兴趣的活动]
    D --> E[查看活动详情]
    E --> F{检查报名状态}
    
    F -->|未报名| G[点击立即报名]
    F -->|已报名| H[显示已报名状态]
    
    G --> I[填写手机号]
    I --> J[点击获取验证码]
    J --> K{验证码发送}
    
    K -->|成功| L[输入验证码]
    K -->|失败| M[显示错误信息]
    M --> J
    
    L --> N[点击确认报名]
    N --> O{报名验证}
    
    O -->|成功| P[显示报名成功]
    O -->|失败| Q[显示错误信息]
    
    P --> R[发送确认短信]
    Q --> L
    H --> S[查看报名信息]
```

### 2. 活动浏览流程

```mermaid
graph TD
    A[进入活动列表] --> B[查看活动卡片]
    B --> C{筛选活动}
    
    C -->|全部| D[显示所有活动]
    C -->|报名中| E[显示可报名活动]
    C -->|即将开始| F[显示即将开始活动]
    C -->|已结束| G[显示已结束活动]
    
    D --> H[点击活动卡片]
    E --> H
    F --> H
    G --> H
    
    H --> I[查看活动详情]
    I --> J{活动状态判断}
    
    J -->|可报名| K[显示报名按钮]
    J -->|已满员| L[隐藏报名按钮]
    J -->|已结束| M[隐藏报名按钮]
    
    K --> N[进入报名流程]
```

### 3. 验证码处理流程

```mermaid
graph TD
    A[用户请求验证码] --> B{手机号验证}
    
    B -->|格式错误| C[显示格式错误提示]
    B -->|格式正确| D{频率限制检查}
    
    D -->|超出限制| E[显示频率限制提示]
    D -->|未超限| F[调用短信API]
    
    F --> G{API响应}
    
    G -->|成功| H[显示发送成功提示]
    G -->|失败| I[显示发送失败提示]
    
    H --> J[开始60秒倒计时]
    J --> K[禁用发送按钮]
    K --> L{倒计时结束}
    
    L -->|是| M[启用发送按钮]
    L -->|否| N[继续倒计时]
    N --> L
    
    C --> A
    E --> O[等待重试]
    I --> P[提供重试选项]
```

## 管理端交互流程

### 1. 管理员登录流程

```mermaid
graph TD
    A[访问管理后台] --> B{检查登录状态}
    
    B -->|已登录| C[跳转到后台首页]
    B -->|未登录| D[显示登录页面]
    
    D --> E[输入用户名密码]
    E --> F[点击登录按钮]
    F --> G{登录验证}
    
    G -->|成功| H[保存登录状态]
    G -->|失败| I[显示错误信息]
    
    H --> J[跳转到后台首页]
    I --> E
    
    J --> K[显示数据概览]
```

### 2. 活动管理流程

```mermaid
graph TD
    A[进入活动管理] --> B[查看活动列表]
    B --> C{选择操作}
    
    C -->|创建活动| D[打开创建表单]
    C -->|编辑活动| E[打开编辑表单]
    C -->|查看二维码| F[显示二维码弹窗]
    C -->|预览活动| G[打开预览页面]
    C -->|查看统计| H[跳转统计页面]
    
    D --> I[填写活动信息]
    E --> I
    
    I --> J{表单验证}
    
    J -->|验证失败| K[显示错误提示]
    J -->|验证成功| L{选择操作}
    
    L -->|保存草稿| M[保存为草稿状态]
    L -->|发布活动| N[发布活动]
    
    M --> O[生成活动ID]
    N --> O
    O --> P[生成二维码]
    P --> Q[返回活动列表]
    
    K --> I
    
    F --> R{二维码操作}
    R -->|下载| S[下载二维码图片]
    R -->|复制链接| T[复制到剪贴板]
```

### 3. 数据统计流程

```mermaid
graph TD
    A[进入数据统计] --> B[选择活动]
    B --> C[加载统计数据]
    C --> D[显示统计图表]
    D --> E{选择操作}
    
    E -->|查看报名列表| F[显示报名记录]
    E -->|导出数据| G[生成Excel文件]
    E -->|批量补发短信| H[选择手机号]
    
    F --> I{分页浏览}
    I -->|上一页| J[加载上一页数据]
    I -->|下一页| K[加载下一页数据]
    
    G --> L[下载文件]
    
    H --> M[确认发送]
    M --> N{短信发送}
    N -->|成功| O[显示发送结果]
    N -->|失败| P[显示失败信息]
```

## 错误处理流程

### 1. 网络错误处理

```mermaid
graph TD
    A[发起API请求] --> B{网络状态}
    
    B -->|正常| C[等待响应]
    B -->|异常| D[显示网络错误]
    
    C --> E{响应状态}
    
    E -->|200 成功| F[处理响应数据]
    E -->|400 参数错误| G[显示参数错误提示]
    E -->|401 未授权| H[跳转登录页面]
    E -->|403 禁止访问| I[显示权限错误]
    E -->|404 不存在| J[显示资源不存在]
    E -->|500 服务器错误| K[显示服务器错误]
    
    D --> L[提供重试选项]
    G --> M[高亮错误字段]
    K --> L
    
    L --> N{用户选择}
    N -->|重试| A
    N -->|取消| O[返回上一页]
```

### 2. 表单验证流程

```mermaid
graph TD
    A[用户输入] --> B[实时验证]
    B --> C{验证结果}
    
    C -->|通过| D[清除错误提示]
    C -->|失败| E[显示错误信息]
    
    E --> F[高亮错误字段]
    F --> G[阻止表单提交]
    
    D --> H[允许继续输入]
    
    I[表单提交] --> J[全量验证]
    J --> K{所有字段验证}
    
    K -->|通过| L[提交数据]
    K -->|失败| M[显示所有错误]
    
    M --> N[聚焦第一个错误字段]
```

## 页面跳转逻辑

### 1. H5端页面跳转

```mermaid
graph TD
    A[活动列表页] -->|点击活动卡片| B[活动详情页]
    B -->|点击报名按钮| C[报名页面]
    C -->|报名成功| D[成功确认页]
    D -->|确定按钮| B
    
    B -->|返回按钮| A
    C -->|返回按钮| B
    
    E[扫描二维码] -->|直接访问| B
```

### 2. 管理端页面跳转

```mermaid
graph TD
    A[登录页] -->|登录成功| B[数据概览]
    B -->|活动管理| C[活动管理页]
    B -->|报名管理| D[报名管理页]
    B -->|短信管理| E[短信管理页]
    
    C -->|创建活动| F[活动表单弹窗]
    C -->|查看统计| G[统计详情页]
    C -->|预览活动| H[H5预览页面]
    
    F -->|保存成功| C
    G -->|返回| C
    
    I[任意页面] -->|退出登录| A
```

## 状态管理

### 1. 用户端状态

```mermaid
graph TD
    A[页面加载] --> B[检查报名状态]
    B --> C{本地存储检查}
    
    C -->|有记录| D[显示已报名状态]
    C -->|无记录| E[显示未报名状态]
    
    F[报名成功] --> G[更新本地状态]
    G --> H[更新界面显示]
    
    I[页面刷新] --> B
```

### 2. 管理端状态

```mermaid
graph TD
    A[登录成功] --> B[保存Token]
    B --> C[设置用户信息]
    
    D[Token过期] --> E[清除登录状态]
    E --> F[跳转登录页]
    
    G[数据更新] --> H[刷新界面]
    H --> I[更新统计数据]
    
    J[退出登录] --> K[清除所有状态]
    K --> F
```

## 性能优化策略

### 1. 加载优化

- **懒加载**: 图片和非关键资源延迟加载
- **预加载**: 关键页面资源预先加载
- **缓存策略**: 静态资源浏览器缓存
- **压缩优化**: 图片和代码压缩

### 2. 交互优化

- **防抖处理**: 搜索输入防抖
- **节流处理**: 滚动事件节流
- **骨架屏**: 数据加载时显示骨架屏
- **乐观更新**: 操作成功前先更新界面

### 3. 错误恢复

- **自动重试**: 网络错误自动重试
- **降级处理**: 功能不可用时的降级方案
- **离线提示**: 网络断开时的友好提示
- **数据恢复**: 表单数据本地保存和恢复

---

以上交互流程图详细描述了系统的各种用户操作路径和状态变化，为开发团队提供了清晰的实现指导。