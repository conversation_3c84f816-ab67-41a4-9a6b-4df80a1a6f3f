# 在线报名系统 UI 设计规范

## 设计概述

本设计规范为在线报名系统提供统一的视觉标准和交互规范，确保用户界面的一致性和可用性。系统包含H5用户端和管理后台两个主要部分。

## 设计原则

### 1. 简洁明了
- 界面布局清晰，信息层次分明
- 减少不必要的视觉元素，突出核心功能
- 使用简洁的语言和图标

### 2. 移动优先
- H5端优先考虑移动设备体验
- 响应式设计，适配不同屏幕尺寸
- 触摸友好的交互设计

### 3. 一致性
- 统一的颜色、字体、间距规范
- 一致的交互模式和反馈机制
- 统一的组件样式和行为

### 4. 可访问性
- 良好的色彩对比度
- 清晰的文字大小和行高
- 支持键盘导航

## 颜色规范

### 主色调
- **主蓝色**: #3B82F6 (rgb(59, 130, 246))
  - 用于主要按钮、链接、强调元素
  - 悬停状态: #2563EB
  - 激活状态: #1D4ED8

- **渐变色**: 
  - 登录页背景: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  - 按钮渐变: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%)

### 辅助色彩
- **成功绿色**: #10B981 (rgb(16, 185, 129))
- **警告黄色**: #F59E0B (rgb(245, 158, 11))
- **错误红色**: #EF4444 (rgb(239, 68, 68))
- **信息蓝色**: #3B82F6 (rgb(59, 130, 246))

### 中性色彩
- **深灰色**: #111827 (标题文字)
- **中灰色**: #6B7280 (正文文字)
- **浅灰色**: #9CA3AF (辅助文字)
- **边框灰**: #D1D5DB (边框、分割线)
- **背景灰**: #F9FAFB (页面背景)
- **纯白色**: #FFFFFF (卡片背景)

### 状态色彩
- **草稿状态**: #6B7280 (灰色)
- **活跃状态**: #10B981 (绿色)
- **满员状态**: #EF4444 (红色)
- **结束状态**: #6B7280 (灰色)

## 字体规范

### 字体族
- **中文字体**: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif
- **英文字体**: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif

### 字体大小
- **大标题**: 24px (1.5rem) - 页面主标题
- **中标题**: 20px (1.25rem) - 卡片标题
- **小标题**: 18px (1.125rem) - 区块标题
- **正文**: 16px (1rem) - 主要内容
- **小字**: 14px (0.875rem) - 辅助信息
- **微字**: 12px (0.75rem) - 标签、提示

### 字重
- **粗体**: 600 (font-semibold) - 标题、重要信息
- **中等**: 500 (font-medium) - 按钮文字
- **常规**: 400 (font-normal) - 正文内容

### 行高
- **标题行高**: 1.2 - 标题文字
- **正文行高**: 1.5 - 正文内容
- **紧凑行高**: 1.25 - 表格、列表

## 间距规范

### 基础间距单位
使用 4px 为基础单位，采用 8px 网格系统：

- **xs**: 4px (0.25rem)
- **sm**: 8px (0.5rem)
- **md**: 12px (0.75rem)
- **lg**: 16px (1rem)
- **xl**: 20px (1.25rem)
- **2xl**: 24px (1.5rem)
- **3xl**: 32px (2rem)
- **4xl**: 48px (3rem)

### 组件间距
- **页面边距**: 16px (移动端), 24px (桌面端)
- **卡片内边距**: 16px (移动端), 24px (桌面端)
- **按钮内边距**: 12px 16px (小按钮), 16px 24px (大按钮)
- **表单元素间距**: 16px (垂直间距)

## 组件规范

### 按钮组件

#### 主要按钮 (Primary Button)
```css
background: #3B82F6;
color: #FFFFFF;
padding: 12px 24px;
border-radius: 8px;
font-weight: 500;
font-size: 16px;
```

#### 次要按钮 (Secondary Button)
```css
background: #F3F4F6;
color: #374151;
border: 1px solid #D1D5DB;
padding: 12px 24px;
border-radius: 8px;
font-weight: 500;
font-size: 16px;
```

#### 危险按钮 (Danger Button)
```css
background: #EF4444;
color: #FFFFFF;
padding: 12px 24px;
border-radius: 8px;
font-weight: 500;
font-size: 16px;
```

### 输入框组件

#### 标准输入框
```css
border: 1px solid #D1D5DB;
border-radius: 8px;
padding: 12px 16px;
font-size: 16px;
background: #FFFFFF;
```

#### 焦点状态
```css
border-color: #3B82F6;
box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
outline: none;
```

#### 错误状态
```css
border-color: #EF4444;
box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
```

### 卡片组件

#### 标准卡片
```css
background: #FFFFFF;
border: 1px solid #E5E7EB;
border-radius: 12px;
box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
padding: 16px;
```

#### 悬停效果
```css
transform: translateY(-2px);
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
transition: all 0.2s ease;
```

### 状态标签

#### 成功状态
```css
background: #D1FAE5;
color: #065F46;
padding: 4px 12px;
border-radius: 9999px;
font-size: 12px;
font-weight: 500;
```

#### 警告状态
```css
background: #FEF3C7;
color: #92400E;
padding: 4px 12px;
border-radius: 9999px;
font-size: 12px;
font-weight: 500;
```

#### 错误状态
```css
background: #FEE2E2;
color: #991B1B;
padding: 4px 12px;
border-radius: 9999px;
font-size: 12px;
font-weight: 500;
```

## 图标规范

### 图标库
使用 Heroicons 图标库，确保图标风格一致

### 图标尺寸
- **小图标**: 16px (1rem) - 表格、列表中的图标
- **中图标**: 20px (1.25rem) - 按钮、导航中的图标
- **大图标**: 24px (1.5rem) - 页面标题、重要功能图标

### 图标颜色
- **主要图标**: #374151 (深灰色)
- **辅助图标**: #6B7280 (中灰色)
- **激活图标**: #3B82F6 (主蓝色)

## 响应式设计

### 断点设置
- **移动端**: < 768px
- **平板端**: 768px - 1024px
- **桌面端**: > 1024px

### H5端适配
- 最小宽度: 320px
- 最大宽度: 768px
- 主要针对移动设备优化
- 支持横竖屏切换

### 管理后台适配
- 最小宽度: 1024px
- 侧边栏宽度: 256px
- 主内容区自适应宽度

## 交互规范

### 点击反馈
- **按钮点击**: 轻微缩放效果 (scale: 0.98)
- **卡片点击**: 向上移动 2px
- **链接悬停**: 颜色变深

### 加载状态
- **按钮加载**: 显示加载文字，禁用按钮
- **页面加载**: 显示骨架屏或加载动画
- **数据加载**: 显示加载指示器

### 错误处理
- **表单验证**: 实时验证，错误信息显示在字段下方
- **网络错误**: 显示错误提示，提供重试选项
- **操作失败**: 显示具体错误信息

### 成功反馈
- **操作成功**: 显示成功提示信息
- **表单提交**: 显示成功页面或模态框
- **数据更新**: 实时更新界面内容

## 动画效果

### 过渡动画
```css
transition: all 0.2s ease;
```

### 悬停动画
```css
transition: transform 0.2s ease, box-shadow 0.2s ease;
```

### 模态框动画
```css
/* 背景渐入 */
backdrop-filter: blur(4px);
transition: opacity 0.3s ease;

/* 内容弹入 */
transform: scale(0.95);
transition: transform 0.3s ease;
```

## 微信浏览器兼容性

### 特殊处理
- 禁用文字大小自动调整: `-webkit-text-size-adjust: 100%`
- 安全区域适配: `padding-bottom: env(safe-area-inset-bottom)`
- 触摸反馈优化: 避免300ms延迟

### 测试要求
- 在微信内置浏览器中测试所有功能
- 确保表单输入正常工作
- 验证分享功能兼容性

## 可访问性规范

### 颜色对比度
- 正文文字对比度 ≥ 4.5:1
- 大文字对比度 ≥ 3:1
- 非文字元素对比度 ≥ 3:1

### 键盘导航
- 所有交互元素支持Tab键导航
- 焦点状态清晰可见
- 支持Enter和Space键操作

### 语义化标记
- 使用正确的HTML语义标签
- 为图片提供alt属性
- 为表单元素提供label

## 设计资源

### 设计文件
- Figma设计稿: [链接地址]
- 组件库文件: [链接地址]
- 图标资源包: [链接地址]

### 开发资源
- CSS样式库: TailwindCSS
- 图标库: Heroicons
- 字体文件: 系统默认字体

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0 | 2025-01-29 | 初始版本，包含完整设计规范 | AI助手 |

---

本设计规范将随着产品迭代持续更新，请开发团队严格按照规范实施，确保产品体验的一致性。