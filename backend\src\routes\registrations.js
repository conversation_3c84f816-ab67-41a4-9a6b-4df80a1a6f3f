/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名功能API路由
 */

const express = require('express');
const router = express.Router();
const Registration = require('../models/Registration');
const Activity = require('../models/Activity');
const SmsCode = require('../models/SmsCode');
const { authenticateToken } = require('../middleware/auth');

// 提交报名（公开接口）
router.post('/', async (req, res) => {
  try {
    const { eventId, phone, code } = req.body;

    // 参数验证
    if (!eventId || !phone || !code) {
      return res.status(400).json({
        success: false,
        message: '活动ID、手机号和验证码不能为空'
      });
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 验证验证码
    const smsCodeModel = new SmsCode();
    const codeVerification = await smsCodeModel.verifyCode(phone, code, eventId);
    
    if (!codeVerification.valid) {
      return res.status(400).json({
        success: false,
        message: codeVerification.reason
      });
    }

    // 检查活动是否可以报名
    const activityModel = new Activity();
    const canRegisterResult = await activityModel.canRegister(eventId);
    
    if (!canRegisterResult.canRegister) {
      return res.status(400).json({
        success: false,
        message: canRegisterResult.reason
      });
    }

    // 检查是否已经报名
    const registrationModel = new Registration();
    const existingRegistration = await registrationModel.checkRegistration(eventId, phone);
    
    if (existingRegistration) {
      return res.status(400).json({
        success: false,
        message: '您已经报名过该活动'
      });
    }

    // 创建报名记录
    const registrationId = await registrationModel.createRegistration(eventId, phone);

    res.json({
      success: true,
      message: '报名成功',
      data: { registrationId }
    });
  } catch (error) {
    console.error('报名提交错误:', error);
    res.status(500).json({
      success: false,
      message: '报名提交失败'
    });
  }
});

// 查询报名状态（公开接口）
router.get('/check', async (req, res) => {
  try {
    const { eventId, phone } = req.query;

    if (!eventId || !phone) {
      return res.status(400).json({
        success: false,
        message: '活动ID和手机号不能为空'
      });
    }

    const registrationModel = new Registration();
    const registration = await registrationModel.checkRegistration(eventId, phone);

    res.json({
      success: true,
      data: {
        registered: !!registration,
        registration: registration || null
      }
    });
  } catch (error) {
    console.error('查询报名状态错误:', error);
    res.status(500).json({
      success: false,
      message: '查询报名状态失败'
    });
  }
});

// 获取活动报名列表（管理员接口）
router.get('/activity/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    const registrationModel = new Registration();
    const result = await registrationModel.getRegistrationsByEvent(
      eventId, 
      parseInt(page), 
      parseInt(pageSize)
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取报名列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取报名列表失败'
    });
  }
});

// 获取报名统计（管理员接口）
router.get('/stats/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    const registrationModel = new Registration();
    const stats = await registrationModel.getRegistrationStats(eventId);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取报名统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取报名统计失败'
    });
  }
});

module.exports = router;
