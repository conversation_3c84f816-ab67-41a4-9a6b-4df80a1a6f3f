/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名功能API路由
 */

const express = require('express');
const router = express.Router();
const Registration = require('../models/Registration');
const Activity = require('../models/Activity');
const SmsCode = require('../models/SmsCode');
const SmsService = require('../services/smsService');
const RegistrationService = require('../services/registrationService');
const { authenticateToken } = require('../middleware/auth');

// 提交报名（公开接口）
router.post('/', async (req, res) => {
  try {
    const { eventId, phone, code } = req.body;

    // 参数验证
    if (!eventId || !phone || !code) {
      return res.status(400).json({
        success: false,
        message: '活动ID、手机号和验证码不能为空'
      });
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 使用报名服务处理完整流程
    const registrationService = new RegistrationService();
    const result = await registrationService.processRegistration(eventId, phone, code);

    res.json({
      success: true,
      message: '报名成功',
      data: result.data
    });
  } catch (error) {
    console.error('报名提交错误:', error);
    res.status(400).json({
      success: false,
      message: error.message || '报名提交失败'
    });
  }
});

// 查询报名状态（公开接口）
router.get('/check', async (req, res) => {
  try {
    const { eventId, phone } = req.query;

    if (!eventId || !phone) {
      return res.status(400).json({
        success: false,
        message: '活动ID和手机号不能为空'
      });
    }

    const registrationService = new RegistrationService();
    const result = await registrationService.getRegistrationDetails(eventId, phone);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('查询报名状态错误:', error);
    res.status(500).json({
      success: false,
      message: '查询报名状态失败'
    });
  }
});

// 获取活动报名列表（管理员接口）
router.get('/activity/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    const registrationModel = new Registration();
    const result = await registrationModel.getRegistrationsByEvent(
      eventId, 
      parseInt(page), 
      parseInt(pageSize)
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取报名列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取报名列表失败'
    });
  }
});

// 获取报名统计（管理员接口）
router.get('/stats/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    const registrationModel = new Registration();
    const stats = await registrationModel.getRegistrationStats(eventId);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取报名统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取报名统计失败'
    });
  }
});

// 导出报名数据（管理员接口）
router.get('/export/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { format = 'excel' } = req.query;

    const registrationModel = new Registration();
    const data = await registrationModel.exportRegistrations(eventId);

    if (format === 'excel') {
      const XLSX = require('xlsx');

      // 准备Excel数据
      const excelData = data.map((item, index) => ({
        '序号': index + 1,
        '手机号': item.phone,
        '报名时间': item.registration_time,
        '短信状态': item.sms_status,
        '短信发送时间': item.sms_sent_at || '未发送',
        '活动名称': item.activity_title
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // 设置列宽
      ws['!cols'] = [
        { width: 8 },   // 序号
        { width: 15 },  // 手机号
        { width: 20 },  // 报名时间
        { width: 12 },  // 短信状态
        { width: 20 },  // 短信发送时间
        { width: 30 }   // 活动名称
      ];

      XLSX.utils.book_append_sheet(wb, ws, '报名数据');

      // 生成Excel文件
      const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      const fileName = `报名数据_${eventId}_${new Date().toISOString().slice(0, 10)}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

      res.send(buffer);
    } else {
      // 返回JSON格式
      res.json({
        success: true,
        data: data
      });
    }
  } catch (error) {
    console.error('导出报名数据错误:', error);
    res.status(500).json({
      success: false,
      message: '导出报名数据失败'
    });
  }
});

// 批量补发短信（管理员接口）
router.post('/resend-sms/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { phones } = req.body;

    if (!phones || !Array.isArray(phones) || phones.length === 0) {
      return res.status(400).json({
        success: false,
        message: '手机号列表不能为空'
      });
    }

    const registrationService = new RegistrationService();
    const results = await registrationService.batchResendSms(eventId, phones);

    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    res.json({
      success: true,
      message: `批量补发完成，成功${successCount}条，失败${failCount}条`,
      data: {
        total: results.length,
        success: successCount,
        failed: failCount,
        details: results
      }
    });
  } catch (error) {
    console.error('批量补发短信错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '批量补发短信失败'
    });
  }
});

module.exports = router;
